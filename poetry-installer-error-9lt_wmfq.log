Traceback (most recent call last):
  File "/usr/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/snap/code/193/.local/share/pypoetry/venv/lib/python3.8/site-packages/pip/__main__.py", line 22, in <module>
    from pip._internal.cli.main import main as _main
  File "/home/<USER>/snap/code/193/.local/share/pypoetry/venv/lib/python3.8/site-packages/pip/_internal/cli/main.py", line 11, in <module>
    from pip._internal.cli.autocompletion import autocomplete
  File "/home/<USER>/snap/code/193/.local/share/pypoetry/venv/lib/python3.8/site-packages/pip/_internal/cli/autocompletion.py", line 10, in <module>
    from pip._internal.cli.main_parser import create_main_parser
  File "/home/<USER>/snap/code/193/.local/share/pypoetry/venv/lib/python3.8/site-packages/pip/_internal/cli/main_parser.py", line 9, in <module>
    from pip._internal.build_env import get_runnable_pip
  File "/home/<USER>/snap/code/193/.local/share/pypoetry/venv/lib/python3.8/site-packages/pip/_internal/build_env.py", line 17, in <module>
    from pip._internal.cli.spinners import open_spinner
  File "/home/<USER>/snap/code/193/.local/share/pypoetry/venv/lib/python3.8/site-packages/pip/_internal/cli/spinners.py", line 9, in <module>
    from pip._internal.utils.logging import get_indentation
  File "/home/<USER>/snap/code/193/.local/share/pypoetry/venv/lib/python3.8/site-packages/pip/_internal/utils/logging.py", line 29, in <module>
    from pip._internal.utils.misc import ensure_dir
  File "/home/<USER>/snap/code/193/.local/share/pypoetry/venv/lib/python3.8/site-packages/pip/_internal/utils/misc.py", line 42, in <module>
    from pip._internal.locations import get_major_minor_version
  File "/home/<USER>/snap/code/193/.local/share/pypoetry/venv/lib/python3.8/site-packages/pip/_internal/locations/__init__.py", line 66, in <module>
    from . import _distutils
  File "/home/<USER>/snap/code/193/.local/share/pypoetry/venv/lib/python3.8/site-packages/pip/_internal/locations/_distutils.py", line 20, in <module>
    from distutils.cmd import Command as DistutilsCommand
ModuleNotFoundError: No module named 'distutils.cmd'

Traceback:

  File "<stdin>", line 937, in main
  File "<stdin>", line 574, in run
  File "<stdin>", line 595, in install
  File "/usr/lib/python3.8/contextlib.py", line 113, in __enter__
    return next(self.gen)
  File "<stdin>", line 667, in make_env
  File "<stdin>", line 653, in make_env
  File "<stdin>", line 366, in make
  File "<stdin>", line 389, in pip
  File "<stdin>", line 386, in python
  File "<stdin>", line 379, in run
